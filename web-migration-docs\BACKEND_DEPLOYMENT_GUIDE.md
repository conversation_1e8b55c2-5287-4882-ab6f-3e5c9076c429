# 🚀 **NexusScan Backend Deployment Guide**

## **📋 Overview**

This guide documents the persistent deployment of NexusScan backend on Contabo server (************:8090) using systemd service management to ensure the backend continues running after SSH session termination.

---

## **🎯 Deployment Architecture**

### **Server Details**
- **Host**: ************ (Contabo VPS)
- **OS**: Ubuntu 24.04.2 LTS
- **Backend Port**: 8090
- **Metrics Port**: 8001
- **SSH Access**: root@************ (password: 292827sSNex)

### **Directory Structure**
```
/root/nexus-backend/
├── main.py                    # Backend entry point
├── start_nexusscan.sh         # Startup script
├── nexus-venv/               # Python virtual environment
├── .env                      # Environment variables
└── [other backend files]

/etc/systemd/system/
└── nexusscan.service         # Systemd service definition
```

---

## **⚙️ Service Configuration**

### **Systemd Service File** (`/etc/systemd/system/nexusscan.service`)
```ini
[Unit]
Description=NexusScan Backend Service
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/root/nexus-backend
Environment=PORT=8090
Environment=HOST=0.0.0.0
Environment=PATH=/root/nexus-backend/nexus-venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
ExecStart=/root/nexus-backend/nexus-venv/bin/python /root/nexus-backend/main.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### **Startup Script** (`/root/nexus-backend/start_nexusscan.sh`)
```bash
#!/bin/bash
# NexusScan Backend Startup Script

# Set working directory
cd /root/nexus-backend

# Activate virtual environment
source nexus-venv/bin/activate

# Set environment variables
export PORT=8090
export HOST=0.0.0.0

# Start the backend
echo "Starting NexusScan Backend on port 8090..."
python main.py
```

---

## **🔧 Service Management Commands**

### **Basic Operations**
```bash
# Start the service
systemctl start nexusscan

# Stop the service
systemctl stop nexusscan

# Restart the service
systemctl restart nexusscan

# Check service status
systemctl status nexusscan

# Enable auto-start on boot
systemctl enable nexusscan

# Disable auto-start on boot
systemctl disable nexusscan
```

### **Monitoring and Debugging**
```bash
# View real-time logs
journalctl -u nexusscan -f

# View recent logs
journalctl -u nexusscan --since "1 hour ago"

# Check if service is running
systemctl is-active nexusscan

# Check if service is enabled
systemctl is-enabled nexusscan

# View service configuration
systemctl cat nexusscan
```

### **Process and Network Verification**
```bash
# Check if backend process is running
ps aux | grep python | grep main.py

# Check if port 8090 is listening
netstat -tlnp | grep 8090

# Test API health endpoint
curl http://localhost:8090/api/health
curl http://************:8090/api/health
```

---

## **🌐 API Endpoints**

### **Health Check**
```bash
curl http://************:8090/api/health
```
**Expected Response**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2025-07-15T01:01:04.675056+00:00",
    "services": {
      "database": "connected",
      "ai_services": "available",
      "security_tools": "available"
    }
  }
}
```

### **Available Endpoints**
- `GET /api/health` - Service health check
- `GET /api/campaigns` - Campaign management
- `GET /api/scans` - Scan operations
- `WS /ws` - WebSocket connection
- `GET /api/tools/v2/available` - Available security tools
- `GET /metrics` (port 8001) - Prometheus metrics

---

## **🔍 Troubleshooting**

### **Service Won't Start**
```bash
# Check service status for errors
systemctl status nexusscan

# View detailed logs
journalctl -u nexusscan --no-pager

# Check if port is already in use
netstat -tlnp | grep 8090

# Manually test startup script
cd /root/nexus-backend
./start_nexusscan.sh
```

### **Service Keeps Restarting**
```bash
# Check restart count
systemctl status nexusscan

# View logs for error patterns
journalctl -u nexusscan | grep ERROR

# Check virtual environment
source /root/nexus-backend/nexus-venv/bin/activate
python -c "import main"
```

### **API Not Responding**
```bash
# Check if service is running
systemctl is-active nexusscan

# Check port binding
ss -tlnp | grep 8090

# Test local connection
curl -v http://localhost:8090/api/health

# Check firewall (if applicable)
ufw status
```

---

## **🔄 Deployment Updates**

### **Updating Backend Code**
```bash
# Stop the service
systemctl stop nexusscan

# Update code (git pull, file copy, etc.)
cd /root/nexus-backend
# [perform updates]

# Restart the service
systemctl start nexusscan

# Verify deployment
systemctl status nexusscan
curl http://************:8090/api/health
```

### **Environment Variable Changes**
```bash
# Edit environment file
nano /root/nexus-backend/.env

# Or update systemd service
systemctl edit nexusscan

# Reload and restart
systemctl daemon-reload
systemctl restart nexusscan
```

---

## **📈 Current Status**

**✅ Successfully Deployed**: Backend running persistently on Contabo server
**✅ Service Management**: Systemd service with auto-restart enabled
**✅ API Accessibility**: Health endpoint responding from external connections
**✅ Persistent Operation**: Survives SSH session disconnection

**⚠️ Known Issues**:
- Tools endpoint returning internal server error
- AI services showing API key configuration issues
- 36/46 tools still unavailable due to loading errors

**🎯 Next Steps**: Tool functionality restoration and frontend integration testing
